* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.chat-container {
    width: 90%;
    max-width: 800px;
    height: 90vh;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

#username-display {
    font-weight: 500;
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 10px;
    border-radius: 15px;
}

#change-username-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: background 0.3s;
}

#change-username-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.user-count {
    font-size: 0.9rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 10px;
    border-radius: 15px;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
}

.message {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}

.message.own {
    align-items: flex-end;
}

.message.other {
    align-items: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
}

.message.own .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.message.other .message-content {
    background: white;
    color: #333;
    border: 1px solid #e0e0e0;
}

.message-info {
    font-size: 0.75rem;
    color: #666;
    margin-top: 5px;
    display: flex;
    gap: 10px;
    align-items: center;
}

.message-info .username {
    font-weight: 600;
    color: #333;
}

.message-info .timestamp {
    color: #888;
    cursor: help;
}

.message-info .date {
    color: #999;
    font-size: 0.7rem;
}

.system-message {
    text-align: center;
    color: #666;
    font-style: italic;
    margin: 10px 0;
    font-size: 0.9rem;
}

.typing-indicator {
    padding: 10px 20px;
    color: #666;
    font-style: italic;
    font-size: 0.9rem;
    background: #f0f0f0;
    border-top: 1px solid #e0e0e0;
}

.chat-input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e0e0e0;
}

.input-group {
    display: flex;
    gap: 10px;
}

#message-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s;
}

#message-input:focus {
    border-color: #667eea;
}

#send-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: transform 0.2s;
}

#send-btn:hover {
    transform: translateY(-2px);
}

#send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    min-width: 300px;
}

.modal-content h3 {
    margin-bottom: 20px;
    color: #333;
}

#username-input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    margin-bottom: 20px;
    outline: none;
}

#username-input:focus {
    border-color: #667eea;
}

.modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.modal-buttons button {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
}

#save-username-btn {
    background: #667eea;
    color: white;
}

#cancel-username-btn {
    background: #e0e0e0;
    color: #333;
}

/* Status Message */
.status-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #ff4757;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 1001;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-container {
        width: 95%;
        height: 95vh;
    }
    
    .chat-header {
        padding: 15px;
        flex-direction: column;
        gap: 10px;
    }
    
    .user-info {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .message-content {
        max-width: 85%;
    }
}
